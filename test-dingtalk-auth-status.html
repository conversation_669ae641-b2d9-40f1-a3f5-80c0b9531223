<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证状态测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .status-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        .status-label {
            font-weight: bold;
            color: #333;
        }
        .status-value {
            color: #666;
            margin-left: 10px;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
        }
        .success {
            color: #388e3c;
            background: #e8f5e8;
        }
        .warning {
            color: #f57c00;
            background: #fff3e0;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>钉钉认证状态测试</h1>
    
    <div class="status-card">
        <h2>当前认证状态</h2>
        <div id="authStatus">正在检查...</div>
        <button onclick="checkAuthStatus()">刷新状态</button>
        <button onclick="checkStorage()">检查存储</button>
        <button onclick="checkCookies()">检查Cookies</button>
    </div>

    <div class="status-card">
        <h2>存储数据</h2>
        <div id="storageData">正在加载...</div>
    </div>

    <div class="status-card">
        <h2>Cookie数据</h2>
        <div id="cookieData">正在加载...</div>
    </div>

    <div class="status-card">
        <h2>调试日志</h2>
        <div id="debugLog"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let debugLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push({ timestamp, message, type });
            updateDebugLog();
            console.log(`[${timestamp}] ${message}`);
        }

        function updateDebugLog() {
            const logElement = document.getElementById('debugLog');
            logElement.innerHTML = debugLog.map(entry => 
                `<div class="${entry.type}"><strong>${entry.timestamp}</strong>: ${entry.message}</div>`
            ).join('');
        }

        function clearLog() {
            debugLog = [];
            updateDebugLog();
        }

        async function checkAuthStatus() {
            try {
                log('开始检查认证状态...');
                
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('Chrome扩展API不可用');
                }

                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ action: 'getDingTalkAuthStatus' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                log('收到认证状态响应', 'success');
                
                const statusElement = document.getElementById('authStatus');
                
                if (response.success) {
                    const data = response.data;
                    statusElement.innerHTML = `
                        <div class="status-item ${data.isAuthenticated ? 'success' : 'warning'}">
                            <span class="status-label">认证状态:</span>
                            <span class="status-value">${data.isAuthenticated ? '已认证' : '未认证'}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">用户信息:</span>
                            <span class="status-value">${data.userInfo ? JSON.stringify(data.userInfo, null, 2) : '无'}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">组织数量:</span>
                            <span class="status-value">${data.organizations ? data.organizations.length : 0}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">选中组织:</span>
                            <span class="status-value">${data.selectedOrganization ? data.selectedOrganization.corpName : '无'}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">环境:</span>
                            <span class="status-value">${data.environment || '未知'}</span>
                        </div>
                        ${data.statusWarning ? `
                        <div class="status-item warning">
                            <span class="status-label">警告:</span>
                            <span class="status-value">${data.statusWarning}</span>
                        </div>
                        ` : ''}
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    log(`认证状态: ${data.isAuthenticated ? '已认证' : '未认证'}`, data.isAuthenticated ? 'success' : 'warning');
                    if (data.userInfo) {
                        log(`用户名: ${data.userInfo.name || '未知'}`, 'info');
                    }
                } else {
                    statusElement.innerHTML = `<div class="status-item error">获取认证状态失败: ${response.error}</div>`;
                    log(`获取认证状态失败: ${response.error}`, 'error');
                }

            } catch (error) {
                log(`检查认证状态失败: ${error.message}`, 'error');
                document.getElementById('authStatus').innerHTML = 
                    `<div class="status-item error">检查认证状态失败: ${error.message}</div>`;
            }
        }

        async function checkStorage() {
            try {
                log('开始检查存储数据...');
                
                const keys = ['Login', 'MY_ORGS_KEY', 'selectedCorpId', 'userSettings', 'dingtalk_auth_timestamp'];
                const data = await chrome.storage.local.get(keys);
                
                const storageElement = document.getElementById('storageData');
                storageElement.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                log('存储数据检查完成', 'success');
                
            } catch (error) {
                log(`检查存储数据失败: ${error.message}`, 'error');
                document.getElementById('storageData').innerHTML = 
                    `<div class="error">检查存储数据失败: ${error.message}</div>`;
            }
        }

        async function checkCookies() {
            try {
                log('开始检查Cookies...');
                
                const cookies = await chrome.cookies.getAll({ domain: '.dingtalk.com' });
                
                const cookieElement = document.getElementById('cookieData');
                cookieElement.innerHTML = `
                    <div class="status-item">
                        <span class="status-label">Cookie数量:</span>
                        <span class="status-value">${cookies.length}</span>
                    </div>
                    <pre>${JSON.stringify(cookies, null, 2)}</pre>
                `;
                
                log(`找到 ${cookies.length} 个钉钉相关Cookie`, 'success');
                
            } catch (error) {
                log(`检查Cookies失败: ${error.message}`, 'error');
                document.getElementById('cookieData').innerHTML = 
                    `<div class="error">检查Cookies失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，开始自动检查...');
            checkAuthStatus();
            checkStorage();
            checkCookies();
        });
    </script>
</body>
</html>
