<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button.danger { background-color: #dc3545; }
        button.danger:hover { background-color: #c82333; }
        
        .log-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .debug-info {
            background-color: #f1f3f4;
            border-left: 4px solid #ff4444;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .auth-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 6px;
            background-color: #f8f9fa;
        }
        
        .auth-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
        .auth-icon.logged-in { background-color: #28a745; }
        .auth-icon.logged-out { background-color: #dc3545; }
        
        .cookie-simulator {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        
        input, select {
            padding: 6px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🔧 钉钉认证修复功能测试</h1>
    
    <div class="container">
        <h2>📋 测试概述</h2>
        <p>本页面用于测试钉钉认证登录状态更新机制的修复效果，包括：</p>
        <ul>
            <li>✅ Cookie监听器触发条件修复</li>
            <li>✅ 状态保存后通知机制修复</li>
            <li>✅ 存储键定义一致性修复</li>
            <li>✅ 增强调试功能验证</li>
        </ul>
    </div>

    <div class="container">
        <h2>🎯 当前认证状态</h2>
        <div class="auth-status" id="authStatus">
            <div class="auth-icon logged-out" id="authIcon"></div>
            <div>
                <div id="authStatusText">未登录</div>
                <div id="authStatusDetail">等待认证状态检查...</div>
            </div>
        </div>
        <button onclick="checkAuthStatus()">🔄 检查认证状态</button>
        <button onclick="showDebugInfo()" class="danger">🐛 显示调试信息</button>
    </div>

    <div class="container">
        <h2>🍪 Cookie监听器测试</h2>
        <div class="test-section">
            <h3>Cookie变化模拟器</h3>
            <div class="cookie-simulator">
                <div>
                    <label>域名:</label>
                    <select id="cookieDomain">
                        <option value=".dingtalk.com">✅ .dingtalk.com (正确)</option>
                        <option value="dingtalk.com">❌ dingtalk.com (错误)</option>
                        <option value="oa.dingtalk.com">❌ oa.dingtalk.com (错误)</option>
                        <option value="login.dingtalk.com">❌ login.dingtalk.com (错误)</option>
                    </select>
                </div>
                <div>
                    <label>Cookie名称:</label>
                    <select id="cookieName">
                        <option value="account">✅ account (正确)</option>
                        <option value="login_token">❌ login_token (错误)</option>
                        <option value="session_id">❌ session_id (错误)</option>
                    </select>
                </div>
            </div>
            <div>
                <label>Cookie值:</label>
                <input type="text" id="cookieValue" placeholder="输入模拟的cookie值" 
                       value="oauth_k1%3AmU3S71ux%2FGHrVLxmq9DEsxcyW1mOWrGodfQIyzEuyv1x3YVRbpVu2MxjNtm2JP83AzQcN9hPUG53yxTyae1imPjFBdPDvctNnX8QaW7Kuuw%3D">
            </div>
            <button onclick="simulateCookieChange('added')">➕ 模拟Cookie添加</button>
            <button onclick="simulateCookieChange('changed')">🔄 模拟Cookie变更</button>
            <button onclick="simulateCookieChange('removed')">➖ 模拟Cookie删除</button>
        </div>
        
        <div class="test-section">
            <h3>监听器测试结果</h3>
            <div id="cookieTestResults" class="log-area">等待Cookie变化测试...</div>
        </div>
    </div>

    <div class="container">
        <h2>💾 存储同步测试</h2>
        <div class="test-section">
            <h3>存储键测试</h3>
            <button onclick="testStorageKeys()">🔑 测试存储键一致性</button>
            <button onclick="testStateSync()">🔄 测试状态同步</button>
            <button onclick="clearStorage()">🗑️ 清空存储</button>
            <div id="storageTestResults" class="log-area">等待存储测试...</div>
        </div>
    </div>

    <div class="container">
        <h2>📢 通知机制测试</h2>
        <div class="test-section">
            <h3>UI更新通知</h3>
            <button onclick="testNotificationMechanism()">📡 测试通知机制</button>
            <div id="notificationTestResults" class="log-area">等待通知测试...</div>
        </div>
    </div>

    <div class="container">
        <h2>🐛 调试信息</h2>
        <div class="test-section">
            <div id="debugInfoDisplay" class="debug-info">点击"显示调试信息"按钮查看详细信息</div>
        </div>
    </div>

    <!-- 模拟Chrome API -->
    <script>
        // 模拟Chrome扩展API
        window.chrome = {
            storage: {
                local: {
                    data: {},
                    get: function(keys, callback) {
                        const result = {};
                        if (Array.isArray(keys)) {
                            keys.forEach(key => {
                                if (this.data.hasOwnProperty(key)) {
                                    result[key] = this.data[key];
                                }
                            });
                        } else if (typeof keys === 'string') {
                            if (this.data.hasOwnProperty(keys)) {
                                result[keys] = this.data[keys];
                            }
                        } else if (keys === null || keys === undefined) {
                            Object.assign(result, this.data);
                        }
                        setTimeout(() => callback(result), 10);
                    },
                    set: function(items, callback) {
                        Object.assign(this.data, items);
                        setTimeout(() => {
                            if (callback) callback();
                            // 触发存储变化事件
                            if (this.onChanged && this.onChanged.listeners) {
                                this.onChanged.listeners.forEach(listener => {
                                    listener(items, 'local');
                                });
                            }
                        }, 10);
                    },
                    onChanged: {
                        listeners: [],
                        addListener: function(listener) {
                            this.listeners.push(listener);
                        }
                    }
                }
            },
            cookies: {
                listeners: [],
                onChanged: {
                    addListener: function(listener) {
                        chrome.cookies.listeners.push(listener);
                    }
                }
            },
            runtime: {
                listeners: [],
                sendMessage: function(message, callback) {
                    console.log('📤 发送消息:', message);
                    // 模拟消息处理
                    setTimeout(() => {
                        if (callback) callback({success: true});
                        // 通知所有监听器
                        this.listeners.forEach(listener => {
                            listener(message, {}, () => {});
                        });
                    }, 10);
                },
                onMessage: {
                    addListener: function(listener) {
                        chrome.runtime.listeners.push(listener);
                    }
                }
            }
        };
    </script>

    <!-- 加载修复后的认证管理器 -->
    <script src="utils/dingtalk-auth.js"></script>

    <script>
        // 测试日志记录
        const testLogs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLogs.push({message: logEntry, type});
            console.log(logEntry);
        }

        function updateLogArea(elementId, logs = testLogs) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = logs.map(l => l.message).join('\n');
                element.scrollTop = element.scrollHeight;
            }
        }

        // 初始化认证管理器
        let authManager;

        async function initAuthManager() {
            try {
                authManager = new DingTalkAuthManager();
                await authManager.init();
                log('✅ 认证管理器初始化成功');

                // 设置状态变化监听器
                chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                    if (message.type === 'AUTH_STATE_CHANGED') {
                        log(`📢 收到认证状态变化通知: ${JSON.stringify(message.data)}`);
                        updateAuthStatusDisplay(message.data);
                    }
                });

                // 初始状态检查
                checkAuthStatus();

            } catch (error) {
                log(`❌ 认证管理器初始化失败: ${error.message}`, 'error');
            }
        }

        // 更新认证状态显示
        function updateAuthStatusDisplay(authData) {
            const icon = document.getElementById('authIcon');
            const statusText = document.getElementById('authStatusText');
            const statusDetail = document.getElementById('authStatusDetail');

            if (authData && authData.isLoggedIn) {
                icon.className = 'auth-icon logged-in';
                statusText.textContent = '已登录';
                statusDetail.textContent = `用户: ${authData.userInfo?.name || '未知'} | 组织: ${authData.selectedOrg?.corpName || '未知'}`;
            } else {
                icon.className = 'auth-icon logged-out';
                statusText.textContent = '未登录';
                statusDetail.textContent = '请登录钉钉账户';
            }
        }

        // 检查认证状态
        async function checkAuthStatus() {
            try {
                log('🔍 检查当前认证状态...');
                const authState = await authManager.getAuthState();
                log(`📊 认证状态: ${JSON.stringify(authState, null, 2)}`);
                updateAuthStatusDisplay(authState);
                return authState;
            } catch (error) {
                log(`❌ 检查认证状态失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 显示调试信息
        async function showDebugInfo() {
            try {
                log('🐛 获取调试信息...');
                const debugInfo = await authManager.getDebugInfo();

                const debugDisplay = document.getElementById('debugInfoDisplay');
                debugDisplay.innerHTML = `
                    <h4>🔍 调试信息详情</h4>
                    <pre>${JSON.stringify(debugInfo, null, 2)}</pre>
                `;

                log(`🐛 调试信息: ${JSON.stringify(debugInfo, null, 2)}`);
            } catch (error) {
                log(`❌ 获取调试信息失败: ${error.message}`, 'error');
                document.getElementById('debugInfoDisplay').innerHTML = `
                    <h4>❌ 调试信息获取失败</h4>
                    <p>错误: ${error.message}</p>
                `;
            }
        }

        // 模拟Cookie变化
        function simulateCookieChange(changeType) {
            const domain = document.getElementById('cookieDomain').value;
            const name = document.getElementById('cookieName').value;
            const value = document.getElementById('cookieValue').value;

            log(`🍪 模拟Cookie变化: ${changeType} | 域名: ${domain} | 名称: ${name}`);

            const changeInfo = {
                removed: changeType === 'removed',
                cookie: changeType !== 'removed' ? {
                    name: name,
                    value: value,
                    domain: domain,
                    path: '/',
                    secure: true,
                    httpOnly: false
                } : undefined,
                cause: 'explicit'
            };

            // 检查是否应该触发监听器
            const shouldTrigger = domain === ".dingtalk.com" && name === "account";

            if (shouldTrigger) {
                log(`✅ Cookie变化符合监听条件，触发处理器`);
                // 触发所有Cookie监听器
                chrome.cookies.listeners.forEach(listener => {
                    try {
                        listener(changeInfo);
                        log(`📡 Cookie监听器已触发`);
                    } catch (error) {
                        log(`❌ Cookie监听器执行失败: ${error.message}`, 'error');
                    }
                });
            } else {
                log(`⚠️ Cookie变化不符合监听条件 (需要: .dingtalk.com + account)`, 'warning');
            }

            updateLogArea('cookieTestResults');
        }

        // 测试存储键一致性
        async function testStorageKeys() {
            log('🔑 测试存储键一致性...');

            const expectedKeys = {
                LOGIN_KEY: "Login",
                MY_ORGS_KEY: "MY_ORGS_KEY",
                SELECTED_CORP_ID: "selectedCorpId",
                USER_SETTINGS: "userSettings",
                AUTH_TIMESTAMP: "dingtalk_auth_timestamp"
            };

            const actualKeys = authManager.STORAGE_KEYS;

            let allMatch = true;
            for (const [key, expectedValue] of Object.entries(expectedKeys)) {
                if (actualKeys[key] === expectedValue) {
                    log(`✅ 存储键 ${key}: ${expectedValue} (正确)`);
                } else {
                    log(`❌ 存储键 ${key}: 期望 ${expectedValue}, 实际 ${actualKeys[key]}`, 'error');
                    allMatch = false;
                }
            }

            if (allMatch) {
                log('✅ 所有存储键定义正确');
            } else {
                log('❌ 存储键定义存在问题', 'error');
            }

            updateLogArea('storageTestResults');
        }

        // 测试状态同步
        async function testStateSync() {
            log('🔄 测试状态同步机制...');

            // 模拟保存认证状态
            const testAuthData = {
                isLoggedIn: true,
                userInfo: { name: '测试用户', userId: 'test123' },
                selectedOrg: { corpName: '测试组织', corpId: 'corp123' },
                timestamp: Date.now()
            };

            try {
                await authManager.saveAuthState(testAuthData);
                log('✅ 认证状态保存成功');

                // 检查是否触发了通知
                setTimeout(async () => {
                    const savedState = await authManager.getAuthState();
                    if (savedState && savedState.isLoggedIn) {
                        log('✅ 状态同步验证成功');
                    } else {
                        log('❌ 状态同步验证失败', 'error');
                    }
                    updateLogArea('storageTestResults');
                }, 100);

            } catch (error) {
                log(`❌ 状态同步测试失败: ${error.message}`, 'error');
                updateLogArea('storageTestResults');
            }
        }

        // 清空存储
        function clearStorage() {
            chrome.storage.local.data = {};
            log('🗑️ 存储已清空');
            updateLogArea('storageTestResults');
        }

        // 测试通知机制
        function testNotificationMechanism() {
            log('📡 测试通知机制...');

            let notificationReceived = false;

            // 临时监听器
            const testListener = (message, sender, sendResponse) => {
                if (message.type === 'AUTH_STATE_CHANGED') {
                    notificationReceived = true;
                    log('✅ 收到认证状态变化通知');
                    log(`📊 通知数据: ${JSON.stringify(message.data)}`);
                }
            };

            chrome.runtime.onMessage.addListener(testListener);

            // 触发状态变化
            const testData = {
                isLoggedIn: true,
                userInfo: { name: '通知测试用户' },
                timestamp: Date.now()
            };

            authManager.notifyAuthChange(testData);

            setTimeout(() => {
                if (notificationReceived) {
                    log('✅ 通知机制测试成功');
                } else {
                    log('❌ 通知机制测试失败 - 未收到通知', 'error');
                }
                updateLogArea('notificationTestResults');
            }, 100);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 开始初始化测试环境...');
            initAuthManager();
        });
    </script>
</body>
</html>
